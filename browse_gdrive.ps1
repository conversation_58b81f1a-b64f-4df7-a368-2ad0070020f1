# Google Drive Browser Script
# This script helps you browse and download files from your Google Drive

param(
    [string]$Action = "menu",
    [string]$Path = "",
    [string]$DownloadTo = ".\downloads"
)

function Show-Menu {
    Write-Host ""
    Write-Host "=== Google Drive Browser ===" -ForegroundColor Green
    Write-Host "Email: <EMAIL>" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Choose an option:" -ForegroundColor White
    Write-Host "1. List all folders in root" -ForegroundColor Yellow
    Write-Host "2. List all files in root" -ForegroundColor Yellow
    Write-Host "3. Browse a specific folder" -ForegroundColor Yellow
    Write-Host "4. Search for files" -ForegroundColor Yellow
    Write-Host "5. Download everything" -ForegroundColor Yellow
    Write-Host "6. Download specific folder" -ForegroundColor Yellow
    Write-Host "7. Download specific file" -ForegroundColor Yellow
    Write-Host "8. Show drive info" -ForegroundColor Yellow
    Write-Host "9. Exit" -ForegroundColor Yellow
    Write-Host ""
}

function Test-RcloneConfig {
    Write-Host "Testing Google Drive connection..." -ForegroundColor Yellow
    try {
        $remotes = & .\rclone.bat listremotes 2>&1
        if ($remotes -match "gdrive:") {
            Write-Host "✓ Google Drive is configured!" -ForegroundColor Green
            return $true
        } else {
            Write-Host "✗ Google Drive not configured. Please run .\configure_gdrive.ps1 first" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "✗ Error checking rclone config: $_" -ForegroundColor Red
        return $false
    }
}

function Show-Folders {
    Write-Host "📁 Folders in your Google Drive:" -ForegroundColor Cyan
    & .\rclone.bat lsd gdrive:
}

function Show-Files {
    param([string]$FolderPath = "")
    if ($FolderPath) {
        Write-Host "📄 Files in folder '$FolderPath':" -ForegroundColor Cyan
        & .\rclone.bat ls "gdrive:$FolderPath"
    } else {
        Write-Host "📄 All files in your Google Drive root:" -ForegroundColor Cyan
        & .\rclone.bat ls gdrive:
    }
}

function Search-Files {
    $searchTerm = Read-Host "Enter search term"
    Write-Host "🔍 Searching for '$searchTerm'..." -ForegroundColor Cyan
    & .\rclone.bat ls gdrive: | findstr /i $searchTerm
}

function Download-Everything {
    Write-Host "⬇️ Downloading all files from Google Drive..." -ForegroundColor Cyan
    Write-Host "This may take a while depending on your drive size." -ForegroundColor Yellow
    
    # Create downloads directory
    if (!(Test-Path $DownloadTo)) {
        New-Item -Path $DownloadTo -ItemType Directory -Force
        Write-Host "Created directory: $DownloadTo" -ForegroundColor Green
    }
    
    # Show what would be downloaded first
    Write-Host "Preview of what will be downloaded:" -ForegroundColor Yellow
    & .\rclone.bat copy gdrive: $DownloadTo --dry-run
    
    $confirm = Read-Host "Continue with download? (y/n)"
    if ($confirm -eq 'y' -or $confirm -eq 'Y') {
        & .\rclone.bat copy gdrive: $DownloadTo --progress
        Write-Host "✓ Download completed!" -ForegroundColor Green
    }
}

function Download-Folder {
    $folderName = Read-Host "Enter folder name to download"
    Write-Host "⬇️ Downloading folder '$folderName'..." -ForegroundColor Cyan
    
    if (!(Test-Path $DownloadTo)) {
        New-Item -Path $DownloadTo -ItemType Directory -Force
    }
    
    & .\rclone.bat copy "gdrive:$folderName" "$DownloadTo\$folderName" --progress
    Write-Host "✓ Folder download completed!" -ForegroundColor Green
}

function Download-File {
    $fileName = Read-Host "Enter file name to download"
    Write-Host "⬇️ Downloading file '$fileName'..." -ForegroundColor Cyan
    
    if (!(Test-Path $DownloadTo)) {
        New-Item -Path $DownloadTo -ItemType Directory -Force
    }
    
    & .\rclone.bat copy "gdrive:$fileName" $DownloadTo --progress
    Write-Host "✓ File download completed!" -ForegroundColor Green
}

function Show-DriveInfo {
    Write-Host "💾 Google Drive Information:" -ForegroundColor Cyan
    & .\rclone.bat about gdrive:
}

# Main script logic
if (!(Test-RcloneConfig)) {
    exit 1
}

if ($Action -eq "menu") {
    do {
        Show-Menu
        $choice = Read-Host "Enter your choice (1-9)"
        
        switch ($choice) {
            "1" { Show-Folders }
            "2" { Show-Files }
            "3" { 
                $folder = Read-Host "Enter folder name"
                Show-Files -FolderPath $folder
            }
            "4" { Search-Files }
            "5" { Download-Everything }
            "6" { Download-Folder }
            "7" { Download-File }
            "8" { Show-DriveInfo }
            "9" { 
                Write-Host "Goodbye!" -ForegroundColor Green
                break
            }
            default { Write-Host "Invalid choice. Please try again." -ForegroundColor Red }
        }
        
        if ($choice -ne "9") {
            Write-Host ""
            Read-Host "Press Enter to continue"
        }
    } while ($choice -ne "9")
}
