# Google Drive Configuration Helper
# This script will help you configure rclone for Google Drive access

Write-Host "=== Google Drive Configuration Helper ===" -ForegroundColor Green
Write-Host ""

# Check if rclone is working
Write-Host "Testing rclone..." -ForegroundColor Yellow
try {
    $version = & .\rclone.bat version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Rclone is working!" -ForegroundColor Green
    } else {
        Write-Host "✗ Rclone test failed" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "✗ Error running rclone: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Now we need to configure Google Drive access." -ForegroundColor Cyan
Write-Host "This will open an interactive configuration. Follow these steps:" -ForegroundColor White
Write-Host ""
Write-Host "1. Type 'n' for new remote" -ForegroundColor Yellow
Write-Host "2. Name it 'gdrive' (or any name you prefer)" -ForegroundColor Yellow
Write-Host "3. Look for 'Google Drive' in the list and enter its number (usually 15)" -ForegroundColor Yellow
Write-Host "4. Leave client_id blank (just press Enter)" -ForegroundColor Yellow
Write-Host "5. Leave client_secret blank (just press Enter)" -ForegroundColor Yellow
Write-Host "6. Choose '1' for full access to all files" -ForegroundColor Yellow
Write-Host "7. Leave root_folder_id blank (press Enter)" -ForegroundColor Yellow
Write-Host "8. Leave service_account_file blank (press Enter)" -ForegroundColor Yellow
Write-Host "9. Type 'n' for advanced config" -ForegroundColor Yellow
Write-Host "10. Type 'y' for auto config (this will open your browser)" -ForegroundColor Yellow
Write-Host "11. Sign in with your Google account: <EMAIL>" -ForegroundColor Yellow
Write-Host "12. Type 'n' for team drive (unless you need it)" -ForegroundColor Yellow
Write-Host "13. Type 'y' to confirm the configuration" -ForegroundColor Yellow
Write-Host "14. Type 'q' to quit the configuration" -ForegroundColor Yellow
Write-Host ""

$response = Read-Host "Ready to start configuration? (y/n)"
if ($response -eq 'y' -or $response -eq 'Y') {
    Write-Host "Starting rclone config..." -ForegroundColor Green
    & .\rclone.bat config
} else {
    Write-Host "Configuration cancelled. Run this script again when ready." -ForegroundColor Yellow
}
